'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChatWidget } from './chat-widget';
import { useNotifications } from '@/hooks/use-notifications';
import { cn } from '@/lib/utils';
import { MessageCircle, X } from 'lucide-react';

interface MessagingFabProps {
  className?: string;
}

export function MessagingFab({ className }: MessagingFabProps) {
  const [isWidgetOpen, setIsWidgetOpen] = useState(false);
  const { notifications } = useNotifications();

  // <PERSON><PERSON> bildiri<PERSON>ini say
  const messageNotificationCount = notifications.filter(
    notification =>
      notification.title === 'Yeni Mesaj' &&
      !notification.is_read
  ).length;

  return (
    <>
      {/* Chat Widget */}
      {isWidgetOpen && (
        <ChatWidget onClose={() => setIsWidgetOpen(false)} />
      )}

      {/* Floating Action Button */}
      <Button
        onClick={() => setIsWidgetOpen(!isWidgetOpen)}
        className={cn(
          "fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50",
          "bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-primary-foreground",
          "border-2 border-background backdrop-blur-sm",
          "hover:scale-110 active:scale-95",
          isWidgetOpen && "bg-gradient-to-r from-destructive to-destructive/90 hover:from-destructive/90 hover:to-destructive rotate-45",
          className
        )}
        size="lg"
      >
        <div className="relative">
          {isWidgetOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <>
              <MessageCircle className="h-6 w-6" />
              {messageNotificationCount > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center animate-pulse"
                >
                  {messageNotificationCount > 9 ? '9+' : messageNotificationCount}
                </Badge>
              )}
            </>
          )}
        </div>
      </Button>
    </>
  );
}
