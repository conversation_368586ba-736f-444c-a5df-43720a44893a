import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ChatWidget } from '@/components/messaging/chat-widget';
import { MessageCircle } from 'lucide-react';

export function ChatWidgetHeaderButton() {
  const [isWidgetOpen, setIsWidgetOpen] = useState(false);

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        aria-label="Mesajlar"
        onClick={() => setIsWidgetOpen((v) => !v)}
        className="rounded-full hover:bg-primary/10"
      >
        <MessageCircle className="h-6 w-6 text-primary" />
      </Button>
      {isWidgetOpen && (
        <div className="absolute right-0 top-full mt-2 z-50 w-80 max-w-[95vw]">
          <ChatWidget onClose={() => setIsWidgetOpen(false)} />
        </div>
      )}
    </div>
  );
}
