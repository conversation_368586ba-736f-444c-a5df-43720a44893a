/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> sistemi performans testleri
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useMessaging } from '@/hooks/use-messaging';
import { ChatWidget } from '../chat-widget';
import { MessageItem } from '../message-item';

// Mock Supabase
jest.mock('@/lib/supabase/client', () => ({
  createClient: () => ({
    auth: {
      getUser: () => Promise.resolve({ data: { user: { id: 'test-user' } } })
    },
    channel: () => ({
      on: () => ({ subscribe: () => {} }),
      subscribe: () => {},
      unsubscribe: () => {}
    })
  })
}));

// Mock messaging actions
jest.mock('@/lib/actions/messaging/messaging-actions', () => ({
  getUserConversations: () => Promise.resolve([]),
  getConversationMessages: () => Promise.resolve([]),
  sendMessage: () => Promise.resolve({ success: true, message: { id: '1', content: 'test' } })
}));

describe('Messaging Performance Tests', () => {
  describe('useMessaging Hook', () => {
    it('should not create multiple subscriptions on re-renders', async () => {
      const TestComponent = () => {
        const { conversations } = useMessaging();
        return <div data-testid="conversations">{conversations.length}</div>;
      };

      const { rerender } = render(<TestComponent />);
      
      // Re-render birkaç kez
      rerender(<TestComponent />);
      rerender(<TestComponent />);
      
      // Subscription'ların tekrar oluşturulmadığını kontrol et
      expect(screen.getByTestId('conversations')).toBeInTheDocument();
    });

    it('should handle optimistic updates correctly', async () => {
      const TestComponent = () => {
        const { sendMessage, messages } = useMessaging();
        
        const handleSend = () => {
          sendMessage('test message', 'conv-1');
        };

        return (
          <div>
            <button onClick={handleSend} data-testid="send-btn">Send</button>
            <div data-testid="messages-count">{messages.length}</div>
          </div>
        );
      };

      render(<TestComponent />);
      
      const sendBtn = screen.getByTestId('send-btn');
      fireEvent.click(sendBtn);

      // Optimistic update ile mesaj sayısının artması beklenir
      await waitFor(() => {
        expect(screen.getByTestId('messages-count')).toHaveTextContent('1');
      });
    });
  });

  describe('MessageItem Component', () => {
    const mockMessage = {
      id: '1',
      content: 'Test message',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      conversation_id: 'conv-1',
      sender_id: 'user-1',
      message_type: 'text' as const,
      edited_at: null,
      reply_to_id: null,
      sender: {
        id: 'user-1',
        full_name: 'Test User',
        avatar_url: null
      }
    };

    it('should render without unnecessary re-renders', () => {
      const { rerender } = render(
        <MessageItem 
          message={mockMessage} 
          isOwn={true} 
          showAvatar={true}
        />
      );

      // Aynı props ile re-render
      rerender(
        <MessageItem 
          message={mockMessage} 
          isOwn={true} 
          showAvatar={true}
        />
      );

      expect(screen.getByText('Test message')).toBeInTheDocument();
    });

    it('should handle large message content efficiently', () => {
      const largeMessage = {
        ...mockMessage,
        content: 'A'.repeat(1000) // 1000 karakter
      };

      const startTime = performance.now();
      render(
        <MessageItem 
          message={largeMessage} 
          isOwn={false} 
          showAvatar={true}
        />
      );
      const endTime = performance.now();

      // Render süresi 100ms'den az olmalı
      expect(endTime - startTime).toBeLessThan(100);
    });
  });

  describe('ChatWidget Component', () => {
    it('should handle scroll events efficiently', async () => {
      render(<ChatWidget onClose={() => {}} />);

      // Scroll area'yı bul
      const scrollArea = screen.getByRole('region');
      
      // Scroll event'ini simüle et
      fireEvent.scroll(scrollArea, { target: { scrollTop: 50 } });

      // Event handler'ın çalıştığını kontrol et
      expect(scrollArea).toBeInTheDocument();
    });

    it('should debounce search input', async () => {
      render(<ChatWidget onClose={() => {}} />);

      // Search view'a geç
      const searchBtn = screen.getByText('Ara');
      fireEvent.click(searchBtn);

      const searchInput = screen.getByPlaceholderText(/ara/i);
      
      // Hızlı typing simüle et
      fireEvent.change(searchInput, { target: { value: 'a' } });
      fireEvent.change(searchInput, { target: { value: 'ab' } });
      fireEvent.change(searchInput, { target: { value: 'abc' } });

      // Debounce çalışmalı
      await waitFor(() => {
        expect(searchInput).toHaveValue('abc');
      });
    });
  });

  describe('Memory Leak Prevention', () => {
    it('should cleanup subscriptions on unmount', () => {
      const { unmount } = render(<ChatWidget onClose={() => {}} />);
      
      // Component'i unmount et
      unmount();
      
      // Memory leak olmadığını kontrol et (gerçek test için daha detaylı kontrol gerekir)
      expect(true).toBe(true);
    });
  });

  describe('Performance Benchmarks', () => {
    it('should render 100 messages in under 500ms', () => {
      const messages = Array.from({ length: 100 }, (_, i) => ({
        ...mockMessage,
        id: `msg-${i}`,
        content: `Message ${i}`
      }));

      const startTime = performance.now();
      
      render(
        <div>
          {messages.map(message => (
            <MessageItem 
              key={message.id}
              message={message} 
              isOwn={i % 2 === 0} 
              showAvatar={true}
            />
          ))}
        </div>
      );
      
      const endTime = performance.now();
      
      // 100 mesaj 500ms'den az sürede render olmalı
      expect(endTime - startTime).toBeLessThan(500);
    });
  });
});
