'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Send, X, Reply } from 'lucide-react';
import { MessageWithSender } from '@/types/messaging';

interface MessageInputProps {
  onSendMessage: (content: string) => Promise<void>;
  replyTo?: MessageWithSender | null;
  onCancelReply?: () => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export function MessageInput({
  onSendMessage,
  replyTo,
  onCancelReply,
  disabled = false,
  placeholder = 'Mesajınızı yazın...',
  className,
}: MessageInputProps) {
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Textarea'yı otomatik boyutlandır
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  };

  // Mesaj gönder
  const handleSendMessage = async () => {
    const trimmedMessage = message.trim();
    if (!trimmedMessage || isSending || disabled) return;

    try {
      setIsSending(true);
      await onSendMessage(trimmedMessage);
      setMessage('');
      
      // Textarea yüksekliğini sıfırla
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
      
      // Yanıtı temizle
      onCancelReply?.();
    } catch (error) {
      console.error('Mesaj gönderme hatası:', error);
    } finally {
      setIsSending(false);
    }
  };

  // Enter tuşu ile gönder (Shift+Enter ile yeni satır)
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Mesaj değiştiğinde textarea yüksekliğini ayarla
  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  // Yanıt modu değiştiğinde focus
  useEffect(() => {
    if (replyTo && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [replyTo]);

  return (
    <div className={cn('border-t bg-background p-4', className)}>
      {/* Yanıtlanan mesaj gösterimi */}
      {replyTo && (
        <Card className="mb-3 p-3 bg-muted/50 border-l-4 border-l-primary">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <Reply className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium text-foreground">
                  {replyTo.sender?.full_name || 'Bilinmeyen Kullanıcı'} yanıtlanıyor
                </span>
              </div>
              <div className="text-sm text-muted-foreground line-clamp-2">
                {replyTo.content}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancelReply}
              className="h-6 w-6 p-0 flex-shrink-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      )}

      {/* Mesaj giriş alanı */}
      <div className="flex items-end gap-2">
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled || isSending}
            className={cn(
              'min-h-[44px] max-h-[120px] resize-none pr-12',
              'focus-visible:ring-1 focus-visible:ring-primary'
            )}
            rows={1}
          />
          
          {/* Karakter sayısı (isteğe bağlı) */}
          {message.length > 0 && (
            <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
              {message.length}
            </div>
          )}
        </div>

        {/* Gönder butonu */}
        <Button
          onClick={handleSendMessage}
          disabled={!message.trim() || isSending || disabled}
          size="sm"
          className="h-11 w-11 p-0 flex-shrink-0"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>

      {/* Yardım metni */}
      <div className="mt-2 text-xs text-muted-foreground">
        <span className="hidden sm:inline">
          Enter ile gönder, Shift+Enter ile yeni satır
        </span>
        <span className="sm:hidden">
          Mesaj göndermek için gönder butonuna dokunun
        </span>
      </div>
    </div>
  );
}
