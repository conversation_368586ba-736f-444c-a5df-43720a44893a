'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { ConversationWithParticipants } from '@/types/messaging';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { Search, Plus, Users, MessageCircle } from 'lucide-react';

interface ConversationListProps {
  conversations: ConversationWithParticipants[];
  activeConversationId?: string | null;
  onSelectConversation: (conversationId: string) => void;
  onCreateConversation?: () => void;
  currentUserId: string;
  className?: string;
}

export function ConversationList({
  conversations,
  activeConversationId,
  onSelectConversation,
  onCreateConversation,
  currentUserId,
  className,
}: ConversationListProps) {
  const [searchQuery, setSearchQuery] = useState('');

  // Konuşmaları filtrele
  const filteredConversations = conversations.filter(conversation => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    
    // Konuşma adında ara
    if (conversation.name?.toLowerCase().includes(query)) {
      return true;
    }
    
    // Katılımcı adlarında ara
    return conversation.participants.some(participant => 
      participant.profile.full_name?.toLowerCase().includes(query) ||
      participant.profile.email?.toLowerCase().includes(query)
    );
  });

  // Konuşma adını al
  const getConversationName = (conversation: ConversationWithParticipants) => {
    if (conversation.name) {
      return conversation.name;
    }
    
    // Birebir konuşma için diğer kullanıcının adını al
    if (conversation.type === 'direct') {
      const otherParticipant = conversation.participants.find(
        p => p.profile_id !== currentUserId
      );
      return otherParticipant?.profile.full_name || 'Bilinmeyen Kullanıcı';
    }
    
    // Grup konuşması için katılımcı adlarını listele
    const participantNames = conversation.participants
      .filter(p => p.profile_id !== currentUserId)
      .map(p => p.profile.full_name || 'Bilinmeyen')
      .slice(0, 3)
      .join(', ');
    
    return participantNames || 'Grup Konuşması';
  };

  // Konuşma avatarını al
  const getConversationAvatar = (conversation: ConversationWithParticipants) => {
    if (conversation.type === 'direct') {
      const otherParticipant = conversation.participants.find(
        p => p.profile_id !== currentUserId
      );
      return otherParticipant?.profile.avatar_url;
    }
    return null; // Grup konuşmaları için varsayılan avatar
  };

  // Konuşma başlangıç harflerini al
  const getConversationInitials = (conversation: ConversationWithParticipants) => {
    const name = getConversationName(conversation);
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Son mesaj zamanını formatla
  const formatLastMessageTime = (date: string) => {
    const messageDate = new Date(date);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return format(messageDate, 'HH:mm', { locale: tr });
    } else if (diffDays === 1) {
      return 'Dün';
    } else if (diffDays < 7) {
      return format(messageDate, 'EEEE', { locale: tr });
    } else {
      return format(messageDate, 'dd.MM.yyyy', { locale: tr });
    }
  };

  // Çevrimiçi durumu kontrol et (şimdilik false döndür, presence hook'u eklendiğinde güncellenecek)
  const getOnlineStatus = (_conversation: ConversationWithParticipants) => {
    // TODO: Presence hook entegrasyonu
    return false;
  };

  return (
    <div className={cn('flex flex-col h-full bg-background', className)}>
      {/* Başlık */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold">Mesajlar</h2>
          {onCreateConversation && (
            <Button
              variant="outline"
              size="sm"
              onClick={onCreateConversation}
              className="h-8 w-8 p-0"
            >
              <Plus className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {/* Arama */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Konuşmalarda ara..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      {/* Konuşma listesi */}
      <ScrollArea className="flex-1">
        {filteredConversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full p-8 text-center">
            <MessageCircle className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">
              {searchQuery ? 'Konuşma bulunamadı' : 'Henüz konuşma yok'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery 
                ? 'Arama kriterlerinize uygun konuşma bulunamadı'
                : 'Yeni bir konuşma başlatarak mesajlaşmaya başlayın'
              }
            </p>
            {!searchQuery && onCreateConversation && (
              <Button onClick={onCreateConversation}>
                <Plus className="h-4 w-4 mr-2" />
                Yeni Konuşma
              </Button>
            )}
          </div>
        ) : (
          <div className="p-2">
            {filteredConversations.map((conversation, index) => {
              const isActive = conversation.id === activeConversationId;
              const isOnlineUser = getOnlineStatus(conversation);
              
              return (
                <div key={conversation.id}>
                  <Card
                    className={cn(
                      'p-3 cursor-pointer transition-colors hover:bg-muted/50 border-0 shadow-none',
                      isActive && 'bg-primary/10 border-primary/20'
                    )}
                    onClick={() => onSelectConversation(conversation.id)}
                  >
                    <div className="flex items-center gap-3">
                      {/* Avatar */}
                      <div className="relative">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={getConversationAvatar(conversation) || undefined} />
                          <AvatarFallback>
                            {conversation.type === 'group' ? (
                              <Users className="h-6 w-6" />
                            ) : (
                              getConversationInitials(conversation)
                            )}
                          </AvatarFallback>
                        </Avatar>
                        
                        {/* Çevrimiçi durumu */}
                        {isOnlineUser && (
                          <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-green-500 border-2 border-background rounded-full" />
                        )}
                      </div>

                      {/* Konuşma bilgileri */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-medium truncate">
                            {getConversationName(conversation)}
                          </h3>
                          {conversation.last_message_at && (
                            <span className="text-xs text-muted-foreground">
                              {formatLastMessageTime(conversation.last_message_at)}
                            </span>
                          )}
                        </div>
                        
                        {/* Son mesaj */}
                        {conversation.last_message ? (
                          <p className="text-sm text-muted-foreground truncate">
                            {conversation.last_message.sender_id === currentUserId && 'Sen: '}
                            {conversation.last_message.content}
                          </p>
                        ) : (
                          <p className="text-sm text-muted-foreground italic">
                            Henüz mesaj yok
                          </p>
                        )}
                      </div>

                      {/* Okunmamış mesaj sayısı */}
                      {conversation.unread_count && conversation.unread_count > 0 && (
                        <Badge variant="default" className="h-5 min-w-[20px] text-xs">
                          {conversation.unread_count > 99 ? '99+' : conversation.unread_count}
                        </Badge>
                      )}
                    </div>
                  </Card>
                  
                  {index < filteredConversations.length - 1 && (
                    <Separator className="my-1" />
                  )}
                </div>
              );
            })}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
