'use client';

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { createClient } from '@/lib/supabase/client';
import {
  ConversationWithParticipants,
  MessageWithSender,
  ConversationWithMessages,
  UseMessagingReturn,
} from '@/types/messaging';
import {
  getUserConversations,
  getConversationMessages,
  sendMessage as sendMessageAction,
  editMessage as editMessageAction,
  deleteMessage as deleteMessageAction,
} from '@/lib/actions/messaging/messaging-actions';
import { toast } from 'sonner';

export function useMessaging(): UseMessagingReturn {
  const [conversations, setConversations] = useState<ConversationWithParticipants[]>([]);
  const [activeConversation, setActiveConversationState] = useState<ConversationWithMessages | null>(null);
  const [messages, setMessages] = useState<MessageWithSender[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state'leri
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [isLoadingMoreMessages, setIsLoadingMoreMessages] = useState(false);
  const messagesOffsetRef = useRef(0);
  const MESSAGE_LIMIT = 30; // Sayfa başına mesaj sayısı

  // Supabase client'ı memoize et
  const supabase = useMemo(() => createClient(), []);
  const subscriptionsRef = useRef<any[]>([]);
  const isSubscribedRef = useRef(false);

  // Konuşmaları yükle - optimize edilmiş
  const loadConversations = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await getUserConversations();
      setConversations(data);
      setError(null);
    } catch (err) {
      console.error('Konuşmalar yüklenirken hata:', err);
      setError('Konuşmalar yüklenemedi');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Belirli konuşmanın mesajlarını yükle - pagination ile optimize edilmiş
  const loadMessages = useCallback(async (conversationId: string, reset: boolean = true) => {
    try {
      if (reset) {
        setIsLoading(true);
        messagesOffsetRef.current = 0;
        setHasMoreMessages(true);
      } else {
        setIsLoadingMoreMessages(true);
      }

      const data = await getConversationMessages(
        conversationId,
        MESSAGE_LIMIT,
        messagesOffsetRef.current
      );

      if (reset) {
        setMessages(data);
        messagesOffsetRef.current = data.length;
      } else {
        setMessages(prev => [...data, ...prev]); // Eski mesajları başa ekle
        messagesOffsetRef.current += data.length;
      }

      // Daha az mesaj geldi ise son sayfa
      if (data.length < MESSAGE_LIMIT) {
        setHasMoreMessages(false);
      }

      // Aktif konuşmayı güncelle - conversations state'ini dependency olarak kullanmayalım
      if (reset) {
        setConversations(prevConversations => {
          const conversation = prevConversations.find(c => c.id === conversationId);
          if (conversation) {
            setActiveConversationState({
              ...conversation,
              messages: data,
            });
          }
          return prevConversations; // State'i değiştirme
        });
      }
    } catch (err) {
      console.error('Mesajlar yüklenirken hata:', err);
      setError('Mesajlar yüklenemedi');
    } finally {
      setIsLoading(false);
      setIsLoadingMoreMessages(false);
    }
  }, [MESSAGE_LIMIT]);

  // Daha fazla mesaj yükle
  const loadMoreMessages = useCallback(async () => {
    if (!activeConversation || !hasMoreMessages || isLoadingMoreMessages) {
      return;
    }

    await loadMessages(activeConversation.id, false);
  }, [activeConversation, hasMoreMessages, isLoadingMoreMessages, loadMessages]);

  // Mesaj gönder - optimize edilmiş
  const sendMessage = useCallback(async (content: string, conversationId: string) => {
    if (!content.trim()) return;

    // Kullanıcı bilgisini al
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    // Kullanıcı profil bilgisini al
    const { data: profile } = await supabase
      .from('profiles')
      .select('id, full_name, avatar_url')
      .eq('id', user.id)
      .single();

    // Optimistic update - mesajı hemen ekle
    const tempMessage: MessageWithSender = {
      id: `temp-${Date.now()}`,
      conversation_id: conversationId,
      sender_id: user.id,
      content: content.trim(),
      message_type: 'text',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      edited_at: null,
      reply_to_id: null,
      sender: profile ? {
        id: profile.id,
        full_name: profile.full_name,
        avatar_url: profile.avatar_url
      } : null,
      reply_to: undefined,
    };

    // Aktif konuşmaya ait ise hemen ekle
    if (activeConversation?.id === conversationId) {
      setMessages(prev => [...prev, tempMessage]);
    }

    try {
      const result = await sendMessageAction(conversationId, content);
      if (!result.success) {
        // Hata durumunda temp mesajı kaldır
        if (activeConversation?.id === conversationId) {
          setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));
        }
        toast.error(result.error || 'Mesaj gönderilemedi');
        return;
      }

      // Başarılı ise temp mesajı gerçek mesajla değiştir
      if (result.message && activeConversation?.id === conversationId) {
        setMessages(prev =>
          prev.map(msg =>
            msg.id === tempMessage.id ? result.message! : msg
          )
        );
      }
    } catch (err) {
      console.error('Mesaj gönderme hatası:', err);
      // Hata durumunda temp mesajı kaldır
      if (activeConversation?.id === conversationId) {
        setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));
      }
      toast.error('Mesaj gönderilemedi');
    }
  }, [activeConversation]);

  // Konuşma oluştur - optimize edilmiş
  const createConversation = useCallback(async (participantIds: string[], name?: string): Promise<string> => {
    try {
      const { createConversation } = await import('@/lib/actions/messaging/messaging-actions');
      const result = await createConversation(participantIds, name);

      if (!result.success) {
        toast.error(result.error || 'Konuşma oluşturulamadı');
        throw new Error(result.error);
      }

      if (result.conversation) {
        // Optimistic update - konuşmayı hemen listeye ekle
        setConversations(prev => [result.conversation as ConversationWithParticipants, ...prev]);
        return result.conversation.id;
      }

      throw new Error('Konuşma oluşturulamadı');
    } catch (err) {
      console.error('Konuşma oluşturma hatası:', err);
      toast.error('Konuşma oluşturulamadı');
      throw err;
    }
  }, []);

 

  // Aktif konuşmayı ayarla
  const setActiveConversation = useCallback((conversationId: string | null) => {
    if (!conversationId) {
      setActiveConversationState(null);
      setMessages([]);
      return;
    }
    
    loadMessages(conversationId);
  }, [loadMessages]);

  // Mesaj düzenle
  const editMessage = useCallback(async (messageId: string, newContent: string) => {
    try {
      const result = await editMessageAction(messageId, newContent);
      if (!result.success) {
        toast.error(result.error || 'Mesaj düzenlenemedi');
        return;
      }

      // Mesajı güncelle
      if (result.message) {
        setMessages(prev =>
          prev.map(msg =>
            msg.id === messageId ? result.message! : msg
          )
        );
        toast.success('Mesaj düzenlendi');
      }
    } catch (err) {
      console.error('Mesaj düzenleme hatası:', err);
      toast.error('Mesaj düzenlenemedi');
    }
  }, []);

  // Mesaj sil
  const deleteMessage = useCallback(async (messageId: string) => {
    try {
      const result = await deleteMessageAction(messageId);
      if (!result.success) {
        toast.error(result.error || 'Mesaj silinemedi');
        return;
      }

      // Mesajı listeden kaldır
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      toast.success('Mesaj silindi');
    } catch (err) {
      console.error('Mesaj silme hatası:', err);
      toast.error('Mesaj silinemedi');
    }
  }, []);

  // Optimize edilmiş realtime subscriptions
  useEffect(() => {
    // Zaten subscribe edilmişse tekrar etme
    if (isSubscribedRef.current) {
      return;
    }

    let messagesChannel: any = null;
    let conversationsChannel: any = null;

    const setupRealtimeSubscriptions = async () => {
      try {
        // Önceki subscription'ları temizle
        subscriptionsRef.current.forEach(subscription => {
          subscription?.unsubscribe?.();
        });
        subscriptionsRef.current = [];

        // Mesajlar için subscription - sadece gerekli eventleri dinle
        messagesChannel = supabase
          .channel(`messages-${Date.now()}`)
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'messages',
            },
            async (payload: any) => {
              const rawMessage = payload.new;

              // Sender bilgisini al
              const { data: senderProfile } = await supabase
                .from('profiles')
                .select('id, full_name, avatar_url')
                .eq('id', rawMessage.sender_id)
                .single();

              // Reply_to mesajı varsa onun bilgilerini de al
              let replyToMessage = undefined;
              if (rawMessage.reply_to_id) {
                const { data: replyMessage } = await supabase
                  .from('messages')
                  .select(`
                    *,
                    sender:profiles(id, full_name, avatar_url)
                  `)
                  .eq('id', rawMessage.reply_to_id)
                  .single();

                replyToMessage = replyMessage as MessageWithSender;
              }

              const newMessage: MessageWithSender = {
                ...rawMessage,
                sender: senderProfile || null,
                reply_to: replyToMessage
              };

              // Mesajı ekle
              if (activeConversation && newMessage.conversation_id === activeConversation.id) {
                setMessages(prev => {
                  // Duplicate kontrolü
                  if (prev.some(msg => msg.id === newMessage.id)) {
                    return prev;
                  }
                  return [...prev, newMessage];
                });
              }

              // Konuşma listesini güncelle
              setConversations(prev =>
                prev.map(conv =>
                  conv.id === newMessage.conversation_id
                    ? { ...conv, last_message: newMessage, last_message_at: newMessage.created_at }
                    : conv
                )
              );
            }
          )
          .subscribe();

        // Konuşmalar için subscription - sadece gerekli eventleri dinle
        conversationsChannel = supabase
          .channel(`conversations-${Date.now()}`)
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'conversations',
            },
            () => {
              // Yeni konuşma eklendi, listeyi yenile
              loadConversations();
            }
          )
          .subscribe();

        subscriptionsRef.current = [messagesChannel, conversationsChannel];
        isSubscribedRef.current = true;
      } catch (err) {
        console.error('Realtime subscription hatası:', err);
      }
    };

    setupRealtimeSubscriptions();

    // Cleanup
    return () => {
      messagesChannel?.unsubscribe?.();
      conversationsChannel?.unsubscribe?.();
      subscriptionsRef.current.forEach(subscription => {
        subscription?.unsubscribe?.();
      });
      subscriptionsRef.current = [];
      isSubscribedRef.current = false;
    };
  }, [supabase, loadConversations]); // activeConversation'ı dependency'den çıkardık

  // Sayfa yüklendiğinde konuşmaları getir
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);



  return {
    conversations,
    activeConversation,
    messages,
    isLoading,
    error,
    sendMessage,
    createConversation,
    setActiveConversation,
    // Pagination özellikleri
    hasMoreMessages,
    isLoadingMoreMessages,
    loadMoreMessages,
    // Mesaj düzenleme/silme
    editMessage,
    deleteMessage,
  };
}
