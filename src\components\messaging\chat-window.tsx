'use client';

import { useState, useEffect, useRef } from 'react';
import { ConversationWithMessages, MessageWithSender } from '@/types/messaging';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { MessageItem } from './message-item';
import { MessageInput } from './message-input';
import {  Users, Phone, Video, MoreVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface ChatWindowProps {
  conversation: ConversationWithMessages | null;
  messages: MessageWithSender[];
  currentUserId: string;
  onSendMessage: (content: string) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

export function ChatWindow({
  conversation,
  messages,
  currentUserId,
  onSendMessage,
  isLoading = false,
  className,
}: ChatWindowProps) {
  const [replyTo, setReplyTo] = useState<MessageWithSender | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mesajları en alta kaydır
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };



  // Yeni mesaj geldiğinde otomatik kaydır
  useEffect(() => {
    if (messages.length > 0) {
      const timer = setTimeout(scrollToBottom, 100);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [messages.length]);

  // Konuşma değiştiğinde yanıtı temizle
  useEffect(() => {
    setReplyTo(null);
  }, [conversation?.id]);

  // Konuşma adını al
  const getConversationName = () => {
    if (!conversation) return '';
    
    if (conversation.name) {
      return conversation.name;
    }
    
    if (conversation.type === 'direct') {
      const otherParticipant = conversation.participants.find(
        p => p.profile_id !== currentUserId
      );
      return otherParticipant?.profile.full_name || 'Bilinmeyen Kullanıcı';
    }
    
    const participantNames = conversation.participants
      .filter(p => p.profile_id !== currentUserId)
      .map(p => p.profile.full_name || 'Bilinmeyen')
      .slice(0, 3)
      .join(', ');
    
    return participantNames || 'Grup Konuşması';
  };

  // Konuşma avatarını al
  const getConversationAvatar = () => {
    if (!conversation) return null;
    
    if (conversation.type === 'direct') {
      const otherParticipant = conversation.participants.find(
        p => p.profile_id !== currentUserId
      );
      return otherParticipant?.profile.avatar_url;
    }
    return null;
  };

  // Konuşma başlangıç harflerini al
  const getConversationInitials = () => {
    const name = getConversationName();
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Yanıtla
  const handleReply = (message: MessageWithSender) => {
    setReplyTo(message);
  };

  // Yanıtı iptal et
  const handleCancelReply = () => {
    setReplyTo(null);
  };

  // Mesaj gönder
  const handleSendMessage = async (content: string) => {
    await onSendMessage(content);
  };

  if (!conversation) {
    return (
      <div className={cn('flex items-center justify-center h-full bg-muted/20', className)}>
        <div className="text-center">
          <div className="h-16 w-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
            <Users className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">Konuşma seçin</h3>
          <p className="text-muted-foreground">
            Mesajlaşmaya başlamak için bir konuşma seçin
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col h-full bg-background', className)}>
      {/* Konuşma başlığı */}
      <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={getConversationAvatar() || undefined} />
            <AvatarFallback>
              {conversation.type === 'group' ? (
                <Users className="h-5 w-5" />
              ) : (
                getConversationInitials()
              )}
            </AvatarFallback>
          </Avatar>
          
          <div>
            <h2 className="font-semibold">{getConversationName()}</h2>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              {conversation.type === 'group' && (
                <>
                  <Users className="h-3 w-3" />
                  <span>{conversation.participants.length} katılımcı</span>
                </>
              )}
              {conversation.type === 'direct' && (
                <span>Çevrimiçi</span> // TODO: Gerçek presence durumu
              )}
            </div>
          </div>
        </div>

        {/* Konuşma menüsü */}
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Phone className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Video className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Konuşma Bilgileri</DropdownMenuItem>
              <DropdownMenuItem>Bildirimleri Kapat</DropdownMenuItem>
              <DropdownMenuItem>Mesajları Temizle</DropdownMenuItem>
              <DropdownMenuItem className="text-destructive">
                Konuşmayı Sil
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Mesaj listesi */}
      <div className="flex-1 relative">
        <ScrollArea 
          className="h-full"
          ref={scrollAreaRef}
        >
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2" />
                <p className="text-muted-foreground">Mesajlar yükleniyor...</p>
              </div>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="h-16 w-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-2">Henüz mesaj yok</h3>
                <p className="text-muted-foreground">
                  İlk mesajı göndererek konuşmayı başlatın
                </p>
              </div>
            </div>
          ) : (
            <div className="py-4">
              {messages.map((message, index) => {
                const isOwn = message.sender_id === currentUserId;
                const showAvatar = index === 0 || 
                  messages[index - 1].sender_id !== message.sender_id;
                
                return (
                  <MessageItem
                    key={message.id}
                    message={message}
                    isOwn={isOwn}
                    showAvatar={showAvatar}
                    onReply={handleReply}
                  />
                );
              })}
              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Mesaj giriş alanı */}
      <MessageInput
        onSendMessage={handleSendMessage}
        replyTo={replyTo}
        onCancelReply={handleCancelReply}
        disabled={isLoading}
      />
    </div>
  );
}
