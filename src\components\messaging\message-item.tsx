'use client';

import React, { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { MessageWithSender } from '@/types/messaging';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

import { cn } from '@/lib/utils';
import { MoreVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface MessageItemProps {
  message: MessageWithSender;
  isOwn: boolean;
  showAvatar?: boolean;
  onEdit?: (message: MessageWithSender) => void;
  onDelete?: (message: MessageWithSender) => void;
  className?: string;
}

export const MessageItem = React.memo<MessageItemProps>(
  ({ message, isOwn, showAvatar = true, onEdit, onDelete, className }) => {
    const [showTime, setShowTime] = useState(false);

    const formatTime = (date: string | null) => {
      if (!date) return '';
      return format(new Date(date), 'HH:mm', { locale: tr });
    };

    const formatDate = (date: string | null) => {
      if (!date) return '';
      return format(new Date(date), 'dd MMMM yyyy, HH:mm', { locale: tr });
    };


    const handleEdit = () => {
      onEdit?.(message);
    };

    const handleDelete = () => {
      onDelete?.(message);
    };

    return (
      <div
        className={cn(
          'group hover:bg-muted/30 flex gap-3 px-4 py-2 transition-colors',
          isOwn && 'flex-row-reverse',
          className
        )}
        onClick={() => setShowTime(!showTime)}
      >
        {/* Mesaj içeriği */}
        <div
          className={cn('min-w-0 flex-1', isOwn && 'flex flex-col items-end')}
        >
          {/* Gönderen adı (kendi mesajı değilse) */}
          {!isOwn && showAvatar && (
            <div className="text-foreground mb-1 text-sm font-medium">
              {message.sender?.full_name || 'Bilinmeyen Kullanıcı'}
            </div>
          )}

          {/* Yanıtlanan mesaj */}
          {message.reply_to && (
            <div className="text-muted-foreground line-clamp-2 text-sm">
              {message.reply_to.content}
            </div>
          )}

          {/* Mesaj balonu */}
          <div className="flex items-end">
            <Card
              className={cn(
                'group/message relative p-3',
                isOwn
                  ? 'bg-primary text-primary-foreground ml-auto'
                  : 'bg-card text-card-foreground'
              )}
            >
              <div className="text-sm break-words whitespace-pre-wrap">
                {message.content}
              </div>

              <div className="text-xs">
              {/* Mesaj zamanı ve durumu */}
              <span>{formatTime(message.created_at)}</span>
              {message.edited_at && (
                <span className="italic">(düzenlendi)</span>
              )}
              </div>

              {/* Mesaj menüsü */}
              <div className="absolute -top-1/2 translate-y-1/2 left-4 opacity-0 transition-opacity group-hover/message:opacity-100">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="h-6 w-6 rounded-full p-0"
                    >
                      <MoreVertical className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {isOwn && (
                      <>
                        <DropdownMenuItem onClick={handleEdit}>
                          Düzenle
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={handleDelete}
                        >
                          Sil
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </Card>
          </div>

          {/* Detaylı zaman bilgisi */}
          {showTime && (
            <div
              className={cn(
                'text-muted-foreground mt-1 text-xs',
                isOwn && 'text-right'
              )}
            >
              {formatDate(message.created_at)}
            </div>
          )}
        </div>

        {/* Kendi mesajı için avatar placeholder */}
        {showAvatar && isOwn && <div className="h-8 w-8 flex-shrink-0" />}
      </div>
    );
  }
);

MessageItem.displayName = 'MessageItem';
